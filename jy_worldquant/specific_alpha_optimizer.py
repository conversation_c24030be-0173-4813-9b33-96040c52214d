#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
特定Alpha ID优化脚本
专门处理指定的Alpha ID进行二阶和三阶优化
"""

import os
import sys
import logging
import configparser
from datetime import datetime
from typing import List, Tuple, Dict, Any

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from core import AlphaService, SimulationService, SubmissionService
from common_config import CommonConfig
from common_auth import BrainAuth
from db_utils import setup_database
from machine_lib_concurrent import locate_alpha, login


class SpecificAlphaOptimizer:
    """特定Alpha ID优化器"""
    
    def __init__(self, config_file: str = 'config.ini'):
        """
        初始化优化器
        
        Args:
            config_file: 配置文件路径
        """
        self.config_file = config_file
        self.logger = None
        self.config_parser = None
        self.common_config = None
        self.db_service = None
        self.auth_service = None
        
        # 服务对象
        self.alpha_service = None
        self.simulation_service = None
        self.submission_service = None
        
        # 目标Alpha ID列表
        self.target_alpha_ids = ["voQR35v", "92njMv1", "PAV0Kxq", "voWMxWA"]
        
        # 优化结果存储
        self.optimization_results = {
            'second_order': {},
            'third_order': {},
            'errors': []
        }
    
    def initialize(self) -> bool:
        """
        初始化系统组件
        
        Returns:
            是否初始化成功
        """
        try:
            # 1. 设置日志系统
            self._setup_logging()
            
            # 2. 加载配置
            self._load_configuration()
            
            # 3. 初始化数据库
            self._setup_database()
            
            # 4. 初始化认证服务
            self._setup_auth_service()
            
            # 5. 初始化业务服务
            self._setup_services()
            
            self.logger.info("特定Alpha优化器初始化完成")
            return True
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"优化器初始化失败: {str(e)}")
            else:
                print(f"优化器初始化失败: {str(e)}")
            return False
    
    def _setup_logging(self):
        """设置日志系统"""
        # 创建logs目录
        log_dir = "logs"
        if not os.path.exists(log_dir):
            os.makedirs(log_dir)
        
        # 设置日志文件名
        log_file = os.path.join(
            log_dir, 
            f"specific_alpha_optimizer_{datetime.now().strftime('%Y-%m-%d_%H-%M-%S')}.log"
        )
        
        # 配置日志
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file, encoding='utf-8'),
                logging.StreamHandler(sys.stdout)
            ]
        )
        
        self.logger = logging.getLogger(__name__)
        self.logger.info(f"日志系统已初始化，日志文件: {log_file}")
    
    def _load_configuration(self):
        """加载配置"""
        self.config_parser = configparser.ConfigParser()
        self.config_parser.read(self.config_file, encoding='utf-8')
        self.common_config = CommonConfig.from_config(self.config_parser)
        self.logger.info("配置加载完成")
    
    def _setup_database(self):
        """设置数据库"""
        self.db_service = setup_database(self.config_parser)
        self.logger.info("数据库服务初始化完成")
    
    def _setup_auth_service(self):
        """设置认证服务"""
        self.auth_service = BrainAuth()
        self.logger.info("认证服务初始化完成")
    
    def _setup_services(self):
        """设置业务服务"""
        self.alpha_service = AlphaService(self.common_config, self.auth_service, self.db_service)
        self.simulation_service = SimulationService(self.common_config, self.auth_service, self.db_service)
        self.submission_service = SubmissionService(self.common_config, self.auth_service, self.db_service)
        self.logger.info("业务服务初始化完成")
    
    def get_alpha_info(self, alpha_id: str) -> Dict[str, Any]:
        """
        获取Alpha信息
        
        Args:
            alpha_id: Alpha ID
            
        Returns:
            Alpha信息字典
        """
        try:
            session = login()
            alpha_info = locate_alpha(session, alpha_id)
            
            if alpha_info:
                self.logger.info(f"成功获取Alpha {alpha_id} 信息")
                return {
                    'alpha_id': alpha_id,
                    'expression': alpha_info[1],  # 表达式
                    'sharpe': alpha_info[2],      # Sharpe比率
                    'turnover': alpha_info[3],    # 换手率
                    'fitness': alpha_info[4],     # 适应度
                    'margin': alpha_info[5],      # 边际
                    'decay': alpha_info[7],       # 衰减
                    'success': True
                }
            else:
                self.logger.error(f"无法获取Alpha {alpha_id} 信息")
                return {'alpha_id': alpha_id, 'success': False, 'error': 'Failed to locate alpha'}
                
        except Exception as e:
            self.logger.error(f"获取Alpha {alpha_id} 信息时出错: {str(e)}")
            return {'alpha_id': alpha_id, 'success': False, 'error': str(e)}
    
    def run_second_order_optimization(self, alpha_id: str) -> Dict[str, Any]:
        """
        运行二阶优化
        
        Args:
            alpha_id: Alpha ID
            
        Returns:
            优化结果
        """
        self.logger.info(f"开始对Alpha {alpha_id} 进行二阶优化")
        
        try:
            # 获取Alpha信息
            alpha_info = self.get_alpha_info(alpha_id)
            if not alpha_info['success']:
                return alpha_info
            
            expression = alpha_info['expression']
            decay = alpha_info['decay']
            
            # 检查是否已包含group操作
            if any(op in expression for op in ["group_neutralize", "group_rank", "group_zscore", 
                                             "group_mean", "group_extra", "group_backfill", 
                                             "group_scale", "group_cartesian_product"]):
                self.logger.warning(f"Alpha {alpha_id} 已包含group操作，跳过二阶优化")
                return {
                    'alpha_id': alpha_id,
                    'success': False,
                    'error': 'Alpha already contains group operations'
                }
            
            # 生成二阶Alpha
            second_order_alphas = []
            factory_alphas = self.alpha_service.alpha_factory.create_group_operations(
                "group_neutralize", expression
            )
            
            for factory_alpha in factory_alphas:
                second_order_alphas.append((factory_alpha, decay))
            
            # 过滤新Alpha
            second_order_alphas = self.alpha_service.filter_new_alphas(second_order_alphas)
            
            if second_order_alphas:
                # 限制数量
                if len(second_order_alphas) > 1000:
                    second_order_alphas = second_order_alphas[:1000]
                
                self.logger.info(f"为Alpha {alpha_id} 生成了 {len(second_order_alphas)} 个二阶Alpha")
                
                # 仿真二阶Alpha
                simulation_results = []
                for progress in self.simulation_service.batch_simulate(second_order_alphas):
                    self.logger.info(f"Alpha {alpha_id} 二阶优化仿真进度: {progress}%")
                
                return {
                    'alpha_id': alpha_id,
                    'success': True,
                    'generated_count': len(second_order_alphas),
                    'original_expression': expression,
                    'second_order_expressions': [alpha[0] for alpha in second_order_alphas[:10]]  # 只返回前10个作为示例
                }
            else:
                self.logger.info(f"Alpha {alpha_id} 没有生成新的二阶Alpha")
                return {
                    'alpha_id': alpha_id,
                    'success': True,
                    'generated_count': 0,
                    'message': 'No new second-order alphas generated'
                }
                
        except Exception as e:
            self.logger.error(f"Alpha {alpha_id} 二阶优化失败: {str(e)}")
            return {
                'alpha_id': alpha_id,
                'success': False,
                'error': str(e)
            }
    
    def run_third_order_optimization(self, alpha_id: str) -> Dict[str, Any]:
        """
        运行三阶优化
        
        Args:
            alpha_id: Alpha ID
            
        Returns:
            优化结果
        """
        self.logger.info(f"开始对Alpha {alpha_id} 进行三阶优化")
        
        try:
            # 获取Alpha信息
            alpha_info = self.get_alpha_info(alpha_id)
            if not alpha_info['success']:
                return alpha_info
            
            expression = alpha_info['expression']
            decay = alpha_info['decay']
            
            # 检查是否已包含trade_when操作
            if "trade_when" in expression:
                self.logger.warning(f"Alpha {alpha_id} 已包含trade_when操作，跳过三阶优化")
                return {
                    'alpha_id': alpha_id,
                    'success': False,
                    'error': 'Alpha already contains trade_when operations'
                }
            
            # 生成三阶Alpha
            third_order_alphas = []
            factory_alphas = self.alpha_service.alpha_factory.create_trade_when_operations(
                expression, self.common_config.events
            )
            
            for factory_alpha in factory_alphas:
                third_order_alphas.append((factory_alpha, decay))
            
            # 过滤新Alpha
            third_order_alphas = self.alpha_service.filter_new_alphas(third_order_alphas)
            
            if third_order_alphas:
                # 限制数量
                if len(third_order_alphas) > 1000:
                    third_order_alphas = third_order_alphas[:1000]
                
                self.logger.info(f"为Alpha {alpha_id} 生成了 {len(third_order_alphas)} 个三阶Alpha")
                
                # 仿真三阶Alpha
                for progress in self.simulation_service.batch_simulate(third_order_alphas):
                    self.logger.info(f"Alpha {alpha_id} 三阶优化仿真进度: {progress}%")
                
                return {
                    'alpha_id': alpha_id,
                    'success': True,
                    'generated_count': len(third_order_alphas),
                    'original_expression': expression,
                    'third_order_expressions': [alpha[0] for alpha in third_order_alphas[:10]]  # 只返回前10个作为示例
                }
            else:
                self.logger.info(f"Alpha {alpha_id} 没有生成新的三阶Alpha")
                return {
                    'alpha_id': alpha_id,
                    'success': True,
                    'generated_count': 0,
                    'message': 'No new third-order alphas generated'
                }
                
        except Exception as e:
            self.logger.error(f"Alpha {alpha_id} 三阶优化失败: {str(e)}")
            return {
                'alpha_id': alpha_id,
                'success': False,
                'error': str(e)
            }

    def run_optimization_for_all_targets(self, run_second_order: bool = True, run_third_order: bool = True):
        """
        对所有目标Alpha ID运行优化

        Args:
            run_second_order: 是否运行二阶优化
            run_third_order: 是否运行三阶优化
        """
        self.logger.info(f"开始对 {len(self.target_alpha_ids)} 个目标Alpha进行优化")
        self.logger.info(f"目标Alpha ID: {', '.join(self.target_alpha_ids)}")

        for alpha_id in self.target_alpha_ids:
            self.logger.info(f"处理Alpha ID: {alpha_id}")

            # 二阶优化
            if run_second_order:
                try:
                    second_result = self.run_second_order_optimization(alpha_id)
                    self.optimization_results['second_order'][alpha_id] = second_result

                    if second_result['success']:
                        self.logger.info(f"Alpha {alpha_id} 二阶优化成功，生成了 {second_result.get('generated_count', 0)} 个Alpha")
                    else:
                        self.logger.warning(f"Alpha {alpha_id} 二阶优化失败: {second_result.get('error', 'Unknown error')}")

                except Exception as e:
                    error_msg = f"Alpha {alpha_id} 二阶优化异常: {str(e)}"
                    self.logger.error(error_msg)
                    self.optimization_results['errors'].append(error_msg)

            # 三阶优化
            if run_third_order:
                try:
                    third_result = self.run_third_order_optimization(alpha_id)
                    self.optimization_results['third_order'][alpha_id] = third_result

                    if third_result['success']:
                        self.logger.info(f"Alpha {alpha_id} 三阶优化成功，生成了 {third_result.get('generated_count', 0)} 个Alpha")
                    else:
                        self.logger.warning(f"Alpha {alpha_id} 三阶优化失败: {third_result.get('error', 'Unknown error')}")

                except Exception as e:
                    error_msg = f"Alpha {alpha_id} 三阶优化异常: {str(e)}"
                    self.logger.error(error_msg)
                    self.optimization_results['errors'].append(error_msg)

        # 生成总结报告
        self.generate_summary_report()

    def generate_summary_report(self):
        """生成总结报告"""
        self.logger.info("="*60)
        self.logger.info("优化总结报告")
        self.logger.info("="*60)

        # 二阶优化总结
        if self.optimization_results['second_order']:
            self.logger.info("二阶优化结果:")
            total_second_generated = 0
            successful_second = 0

            for alpha_id, result in self.optimization_results['second_order'].items():
                if result['success']:
                    successful_second += 1
                    generated = result.get('generated_count', 0)
                    total_second_generated += generated
                    self.logger.info(f"  {alpha_id}: 成功 - 生成 {generated} 个二阶Alpha")
                else:
                    self.logger.info(f"  {alpha_id}: 失败 - {result.get('error', 'Unknown error')}")

            self.logger.info(f"二阶优化总计: {successful_second}/{len(self.optimization_results['second_order'])} 成功，共生成 {total_second_generated} 个Alpha")

        # 三阶优化总结
        if self.optimization_results['third_order']:
            self.logger.info("三阶优化结果:")
            total_third_generated = 0
            successful_third = 0

            for alpha_id, result in self.optimization_results['third_order'].items():
                if result['success']:
                    successful_third += 1
                    generated = result.get('generated_count', 0)
                    total_third_generated += generated
                    self.logger.info(f"  {alpha_id}: 成功 - 生成 {generated} 个三阶Alpha")
                else:
                    self.logger.info(f"  {alpha_id}: 失败 - {result.get('error', 'Unknown error')}")

            self.logger.info(f"三阶优化总计: {successful_third}/{len(self.optimization_results['third_order'])} 成功，共生成 {total_third_generated} 个Alpha")

        # 错误总结
        if self.optimization_results['errors']:
            self.logger.info("错误总结:")
            for error in self.optimization_results['errors']:
                self.logger.error(f"  {error}")

        self.logger.info("="*60)
        self.logger.info("优化完成")
        self.logger.info("="*60)


def main():
    """主函数"""
    print("特定Alpha ID优化器")
    print("目标Alpha ID: voQR35v, 92njMv1, PAV0Kxq, voWMxWA")
    print("区域: USA 1 TOP3000")
    print("="*60)

    # 创建优化器实例
    optimizer = SpecificAlphaOptimizer()

    # 初始化
    if not optimizer.initialize():
        print("优化器初始化失败，退出程序")
        return False

    try:
        # 运行优化
        optimizer.run_optimization_for_all_targets(
            run_second_order=True,
            run_third_order=True
        )

        print("优化完成！请查看日志文件获取详细结果。")
        return True

    except KeyboardInterrupt:
        print("\n用户中断，正在退出...")
        return False
    except Exception as e:
        print(f"优化过程中发生错误: {str(e)}")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
