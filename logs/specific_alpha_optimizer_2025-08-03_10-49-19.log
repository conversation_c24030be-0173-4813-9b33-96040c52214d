2025-08-03 10:49:19,918 - __main__ - INFO - 日志系统已初始化，日志文件: logs/specific_alpha_optimizer_2025-08-03_10-49-19.log
2025-08-03 10:49:19,918 - __main__ - INFO - 配置加载完成
2025-08-03 10:49:19,918 - root - INFO - 初始化数据库连接
2025-08-03 10:49:19,920 - root - INFO - 数据库连接成功
2025-08-03 10:49:19,922 - root - INFO - 数据库表创建/检查完成
2025-08-03 10:49:19,922 - root - INFO - check_status 字段已存在，跳过迁移
2025-08-03 10:49:19,922 - root - INFO - 数据库初始化完成
2025-08-03 10:49:19,922 - root - INFO - 创建异步数据库服务
2025-08-03 10:49:19,922 - AsyncDatabaseService - INFO - 异步数据库服务已初始化，线程池大小: 3
2025-08-03 10:49:19,922 - __main__ - INFO - 数据库服务初始化完成
2025-08-03 10:49:19,922 - __main__ - INFO - 认证服务初始化完成
2025-08-03 10:49:19,922 - __main__ - INFO - 业务服务初始化完成
2025-08-03 10:49:19,922 - __main__ - INFO - 特定Alpha优化器初始化完成
2025-08-03 10:49:19,922 - __main__ - INFO - 开始对 4 个目标Alpha进行优化
2025-08-03 10:49:19,922 - __main__ - INFO - 目标Alpha ID: voQR35v, 92njMv1, PAV0Kxq, voWMxWA
2025-08-03 10:49:19,922 - __main__ - INFO - 处理Alpha ID: voQR35v
2025-08-03 10:49:19,922 - __main__ - INFO - 开始对Alpha voQR35v 进行二阶优化
2025-08-03 10:49:21,477 - root - INFO - 登录成功
2025-08-03 10:49:21,477 - root - INFO - 响应内容: {"user":{"id":"YK49234"},"token":{"expiry":14400.0},"permissions":["BEFORE_AND_AFTER_PERFORMANCE_V2","BRAIN_LABS","BRAIN_LABS_JUPYTER_LAB","CONSULTANT","MULTI_SIMULATION","PROD_ALPHAS","REFERRAL","VISUALIZATION","WORKDAY"]}
2025-08-03 10:49:21,945 - __main__ - INFO - 成功获取Alpha voQR35v 信息
2025-08-03 10:49:21,948 - AlphaFactory - INFO - 为区域 USA 加载了 23 个分组，包含类别: ['base']
2025-08-03 10:49:21,952 - root - INFO - 已过滤掉 0 个已回测的alpha，剩余 23 个新alpha待回测
2025-08-03 10:49:21,953 - __main__ - INFO - 为Alpha voQR35v 生成了 23 个二阶Alpha
2025-08-03 10:49:21,953 - SimulationService - INFO - 开始批量仿真，批次大小: 1000
2025-08-03 10:49:21,953 - SimulationService - INFO - 进行第 1/1 批仿真，数量: 23
2025-08-03 10:49:21,953 - SimulationService - INFO - 开始仿真 23 个Alpha
2025-08-03 10:49:21,953 - SimulationService - INFO - 准备仿真 3 个任务
2025-08-03 10:49:23,583 - root - INFO - 登录成功
2025-08-03 10:49:23,583 - root - INFO - 响应内容: {"user":{"id":"YK49234"},"token":{"expiry":14400.0},"permissions":["BEFORE_AND_AFTER_PERFORMANCE_V2","BRAIN_LABS","BRAIN_LABS_JUPYTER_LAB","CONSULTANT","MULTI_SIMULATION","PROD_ALPHAS","REFERRAL","VISUALIZATION","WORKDAY"]}
2025-08-03 10:49:24,022 - SimulationService - INFO - 任务 0 提交成功
2025-08-03 10:49:24,562 - SimulationService - INFO - 任务 1 提交成功
2025-08-03 10:49:24,933 - SimulationService - INFO - 任务 2 提交成功
2025-08-03 10:49:30,258 - SimulationService - ERROR - ❌ 任务 0 失败，包含的Alpha表达式：
2025-08-03 10:49:30,259 - SimulationService - ERROR -    - Alpha: group_neutralize(ts_std_dev(act_q_ndt_value/act_q_nav_value, 100), densify(market)), Decay: 0
2025-08-03 10:49:30,259 - SimulationService - ERROR -    - Alpha: group_neutralize(ts_std_dev(act_q_ndt_value/act_q_nav_value, 100), densify(sector)), Decay: 0
2025-08-03 10:49:30,259 - SimulationService - ERROR -    - Alpha: group_neutralize(ts_std_dev(act_q_ndt_value/act_q_nav_value, 100), densify(industry)), Decay: 0
2025-08-03 10:49:30,259 - SimulationService - ERROR -    - Alpha: group_neutralize(ts_std_dev(act_q_ndt_value/act_q_nav_value, 100), densify(subindustry)), Decay: 0
2025-08-03 10:49:30,259 - SimulationService - ERROR -    - Alpha: group_neutralize(ts_std_dev(act_q_ndt_value/act_q_nav_value, 100), densify(bucket(rank(cap), range='0.1, 1, 0.1'))), Decay: 0
2025-08-03 10:49:30,259 - SimulationService - ERROR -    - Alpha: group_neutralize(ts_std_dev(act_q_ndt_value/act_q_nav_value, 100), densify(bucket(rank(assets), range='0.1, 1, 0.1'))), Decay: 0
2025-08-03 10:49:30,260 - SimulationService - ERROR -    - Alpha: group_neutralize(ts_std_dev(act_q_ndt_value/act_q_nav_value, 100), densify(bucket(group_rank(assets, sector), range='0.1, 1, 0.1'))), Decay: 0
2025-08-03 10:49:30,260 - SimulationService - ERROR -    - Alpha: group_neutralize(ts_std_dev(act_q_ndt_value/act_q_nav_value, 100), densify(bucket(group_rank(cap, sector), range='0.1, 1, 0.1'))), Decay: 0
2025-08-03 10:49:30,260 - SimulationService - ERROR -    - Alpha: group_neutralize(ts_std_dev(act_q_ndt_value/act_q_nav_value, 100), densify(bucket(rank(ts_std_dev(returns,20)), range='0.1, 1, 0.1'))), Decay: 0
2025-08-03 10:49:30,260 - SimulationService - ERROR -    - Alpha: group_neutralize(ts_std_dev(act_q_ndt_value/act_q_nav_value, 100), densify(bucket(rank(close*volume), range='0.1, 1, 0.1'))), Decay: 0
2025-08-03 10:49:30,260 - SimulationService - WARNING - ❌ 任务 0 失败https://api.worldquantbrain.com/simulations/bFQhy5sw5cOcl05ovYz6CK: {"children":["4EJeTS8cA4StbSmo886fkTo","3wIf4Wejl4MUbwE10OszbuVA","3ItoU04GI50w9PTADLatJ70","3FJS1GgSZ55bahDbF5LmTJw","2VaGTp5HY4KLc3TyFU5ijx8","vlUci1Ig5f49r2cEKIZqjg","1hhVyP7Ir5aKanY1aePl7pmN","2HFk6L1mj4Yra1oKAn2ZNw1","2uDF0v4WY5iS9Xp16jQCA10W","3hAiEofsH4xV9f9RHTnR0Lx"],"type":"REGULAR","status":"ERROR"}
2025-08-03 10:49:30,260 - SimulationService - INFO - 正在查询任务 0 的 10 个子任务详情...
2025-08-03 10:49:31,691 - SimulationService - INFO - 📄 任务 0 子任务 1/10 (ID: 4EJeTS8cA4StbSmo886fkTo):
2025-08-03 10:49:31,691 - SimulationService - INFO -    状态: ERROR
2025-08-03 10:49:31,691 - SimulationService - ERROR -    错误信息: Attempted to use unknown variable "act_q_ndt_value"
2025-08-03 10:49:32,519 - SimulationService - INFO - 📄 任务 0 子任务 2/10 (ID: 3wIf4Wejl4MUbwE10OszbuVA):
2025-08-03 10:49:32,519 - SimulationService - INFO -    状态: ERROR
2025-08-03 10:49:32,519 - SimulationService - ERROR -    错误信息: Attempted to use unknown variable "act_q_ndt_value"
2025-08-03 10:49:33,406 - SimulationService - INFO - 📄 任务 0 子任务 3/10 (ID: 3ItoU04GI50w9PTADLatJ70):
2025-08-03 10:49:33,406 - SimulationService - INFO -    状态: ERROR
2025-08-03 10:49:33,406 - SimulationService - ERROR -    错误信息: Attempted to use unknown variable "act_q_ndt_value"
2025-08-03 10:49:34,250 - SimulationService - INFO - 📄 任务 0 子任务 4/10 (ID: 3FJS1GgSZ55bahDbF5LmTJw):
2025-08-03 10:49:34,250 - SimulationService - INFO -    状态: ERROR
2025-08-03 10:49:34,250 - SimulationService - ERROR -    错误信息: Attempted to use unknown variable "act_q_ndt_value"
2025-08-03 10:49:35,081 - SimulationService - INFO - 📄 任务 0 子任务 5/10 (ID: 2VaGTp5HY4KLc3TyFU5ijx8):
2025-08-03 10:49:35,082 - SimulationService - INFO -    状态: ERROR
2025-08-03 10:49:35,082 - SimulationService - ERROR -    错误信息: Attempted to use unknown variable "act_q_ndt_value"
2025-08-03 10:49:36,453 - SimulationService - INFO - 📄 任务 0 子任务 6/10 (ID: vlUci1Ig5f49r2cEKIZqjg):
2025-08-03 10:49:36,453 - SimulationService - INFO -    状态: ERROR
2025-08-03 10:49:36,453 - SimulationService - ERROR -    错误信息: Attempted to use unknown variable "act_q_ndt_value"
2025-08-03 10:49:37,452 - SimulationService - INFO - 📄 任务 0 子任务 7/10 (ID: 1hhVyP7Ir5aKanY1aePl7pmN):
2025-08-03 10:49:37,452 - SimulationService - INFO -    状态: ERROR
2025-08-03 10:49:37,452 - SimulationService - ERROR -    错误信息: Attempted to use unknown variable "act_q_ndt_value"
2025-08-03 10:49:38,345 - SimulationService - INFO - 📄 任务 0 子任务 8/10 (ID: 2HFk6L1mj4Yra1oKAn2ZNw1):
2025-08-03 10:49:38,345 - SimulationService - INFO -    状态: ERROR
2025-08-03 10:49:38,346 - SimulationService - ERROR -    错误信息: Attempted to use unknown variable "act_q_ndt_value"
2025-08-03 10:49:39,177 - SimulationService - INFO - 📄 任务 0 子任务 9/10 (ID: 2uDF0v4WY5iS9Xp16jQCA10W):
2025-08-03 10:49:39,177 - SimulationService - INFO -    状态: ERROR
2025-08-03 10:49:39,177 - SimulationService - ERROR -    错误信息: Attempted to use unknown variable "act_q_ndt_value"
2025-08-03 10:49:40,014 - SimulationService - INFO - 📄 任务 0 子任务 10/10 (ID: 3hAiEofsH4xV9f9RHTnR0Lx):
2025-08-03 10:49:40,014 - SimulationService - INFO -    状态: ERROR
2025-08-03 10:49:40,014 - SimulationService - ERROR -    错误信息: Attempted to use unknown variable "act_q_ndt_value"
2025-08-03 10:49:40,884 - SimulationService - ERROR - ❌ 任务 1 失败，包含的Alpha表达式：
2025-08-03 10:49:40,885 - SimulationService - ERROR -    - Alpha: group_neutralize(ts_std_dev(act_q_ndt_value/act_q_nav_value, 100), densify(pv13_h_min2_3000_sector)), Decay: 0
2025-08-03 10:49:40,885 - SimulationService - ERROR -    - Alpha: group_neutralize(ts_std_dev(act_q_ndt_value/act_q_nav_value, 100), densify(pv13_r2_min20_3000_sector)), Decay: 0
2025-08-03 10:49:40,885 - SimulationService - ERROR -    - Alpha: group_neutralize(ts_std_dev(act_q_ndt_value/act_q_nav_value, 100), densify(pv13_r2_min2_3000_sector)), Decay: 0
2025-08-03 10:49:40,885 - SimulationService - ERROR -    - Alpha: group_neutralize(ts_std_dev(act_q_ndt_value/act_q_nav_value, 100), densify(pv13_h_min2_focused_pureplay_3000_sector)), Decay: 0
2025-08-03 10:49:40,885 - SimulationService - ERROR -    - Alpha: group_neutralize(ts_std_dev(act_q_ndt_value/act_q_nav_value, 100), densify(sta1_top3000c50)), Decay: 0
2025-08-03 10:49:40,885 - SimulationService - ERROR -    - Alpha: group_neutralize(ts_std_dev(act_q_ndt_value/act_q_nav_value, 100), densify(sta1_allc20)), Decay: 0
2025-08-03 10:49:40,885 - SimulationService - ERROR -    - Alpha: group_neutralize(ts_std_dev(act_q_ndt_value/act_q_nav_value, 100), densify(sta1_allc10)), Decay: 0
2025-08-03 10:49:40,885 - SimulationService - ERROR -    - Alpha: group_neutralize(ts_std_dev(act_q_ndt_value/act_q_nav_value, 100), densify(sta1_top3000c20)), Decay: 0
2025-08-03 10:49:40,885 - SimulationService - ERROR -    - Alpha: group_neutralize(ts_std_dev(act_q_ndt_value/act_q_nav_value, 100), densify(sta1_allc5)), Decay: 0
2025-08-03 10:49:40,885 - SimulationService - ERROR -    - Alpha: group_neutralize(ts_std_dev(act_q_ndt_value/act_q_nav_value, 100), densify(sta2_top3000_fact3_c50)), Decay: 0
2025-08-03 10:49:40,885 - SimulationService - WARNING - ❌ 任务 1 失败https://api.worldquantbrain.com/simulations/jSBwBag5cVaoM1b8DTp2On: {"children":["KQwdP42E4Mvcvk18OnuSDab","3G6cUDb49576cls18QQxkoSj","2bpGHjgtG4GZbVsAN4loU55","3zRJDQ6lD4h2bQHWS9X7LH1","2QJdOS1xh4QVbZYhm0gvqxf","2M1LmOdSp58naEIB8ApUHC8","1Xv7zf41z4YaayqW8B5UbAQ","1FcFaT41X4PCaelsAO11M9X","4zqsZX1DO54UcK2HEeJK4Q5","1eUShcbLF5bgahZKtiDIovV"],"type":"REGULAR","status":"ERROR"}
2025-08-03 10:49:40,885 - SimulationService - INFO - 正在查询任务 1 的 10 个子任务详情...
2025-08-03 10:49:41,211 - SimulationService - INFO - 📄 任务 1 子任务 1/10 (ID: KQwdP42E4Mvcvk18OnuSDab):
2025-08-03 10:49:41,211 - SimulationService - INFO -    状态: ERROR
2025-08-03 10:49:41,211 - SimulationService - ERROR -    错误信息: Attempted to use unknown variable "act_q_ndt_value"
2025-08-03 10:49:42,040 - SimulationService - INFO - 📄 任务 1 子任务 2/10 (ID: 3G6cUDb49576cls18QQxkoSj):
2025-08-03 10:49:42,040 - SimulationService - INFO -    状态: ERROR
2025-08-03 10:49:42,040 - SimulationService - ERROR -    错误信息: Attempted to use unknown variable "act_q_ndt_value"
2025-08-03 10:49:43,161 - SimulationService - INFO - 📄 任务 1 子任务 3/10 (ID: 2bpGHjgtG4GZbVsAN4loU55):
2025-08-03 10:49:43,161 - SimulationService - INFO -    状态: ERROR
2025-08-03 10:49:43,161 - SimulationService - ERROR -    错误信息: Attempted to use unknown variable "act_q_ndt_value"
2025-08-03 10:49:43,998 - SimulationService - INFO - 📄 任务 1 子任务 4/10 (ID: 3zRJDQ6lD4h2bQHWS9X7LH1):
2025-08-03 10:49:44,002 - SimulationService - INFO -    状态: ERROR
2025-08-03 10:49:44,002 - SimulationService - ERROR -    错误信息: Attempted to use unknown variable "act_q_ndt_value"
2025-08-03 10:49:44,827 - SimulationService - INFO - 📄 任务 1 子任务 5/10 (ID: 2QJdOS1xh4QVbZYhm0gvqxf):
2025-08-03 10:49:44,827 - SimulationService - INFO -    状态: ERROR
2025-08-03 10:49:44,827 - SimulationService - ERROR -    错误信息: Attempted to use unknown variable "act_q_ndt_value"
2025-08-03 10:49:45,924 - SimulationService - INFO - 📄 任务 1 子任务 6/10 (ID: 2M1LmOdSp58naEIB8ApUHC8):
2025-08-03 10:49:45,925 - SimulationService - INFO -    状态: ERROR
2025-08-03 10:49:45,925 - SimulationService - ERROR -    错误信息: Attempted to use unknown variable "act_q_ndt_value"
2025-08-03 10:49:46,742 - SimulationService - INFO - 📄 任务 1 子任务 7/10 (ID: 1Xv7zf41z4YaayqW8B5UbAQ):
2025-08-03 10:49:46,742 - SimulationService - INFO -    状态: ERROR
2025-08-03 10:49:46,742 - SimulationService - ERROR -    错误信息: Attempted to use unknown variable "act_q_ndt_value"
2025-08-03 10:49:47,667 - SimulationService - INFO - 📄 任务 1 子任务 8/10 (ID: 1FcFaT41X4PCaelsAO11M9X):
2025-08-03 10:49:47,667 - SimulationService - INFO -    状态: ERROR
2025-08-03 10:49:47,667 - SimulationService - ERROR -    错误信息: Attempted to use unknown variable "act_q_ndt_value"
2025-08-03 10:49:48,718 - SimulationService - INFO - 📄 任务 1 子任务 9/10 (ID: 4zqsZX1DO54UcK2HEeJK4Q5):
2025-08-03 10:49:48,718 - SimulationService - INFO -    状态: ERROR
2025-08-03 10:49:48,718 - SimulationService - ERROR -    错误信息: Attempted to use unknown variable "act_q_ndt_value"
2025-08-03 10:49:49,619 - SimulationService - INFO - 📄 任务 1 子任务 10/10 (ID: 1eUShcbLF5bgahZKtiDIovV):
2025-08-03 10:49:49,620 - SimulationService - INFO -    状态: ERROR
2025-08-03 10:49:49,620 - SimulationService - ERROR -    错误信息: Attempted to use unknown variable "act_q_ndt_value"
2025-08-03 10:49:50,773 - SimulationService - ERROR - ❌ 任务 2 失败，包含的Alpha表达式：
2025-08-03 10:49:50,773 - SimulationService - ERROR -    - Alpha: group_neutralize(ts_std_dev(act_q_ndt_value/act_q_nav_value, 100), densify(sta2_top3000_fact4_c20)), Decay: 0
2025-08-03 10:49:50,773 - SimulationService - ERROR -    - Alpha: group_neutralize(ts_std_dev(act_q_ndt_value/act_q_nav_value, 100), densify(sta2_top3000_fact4_c10)), Decay: 0
2025-08-03 10:49:50,773 - SimulationService - ERROR -    - Alpha: group_neutralize(ts_std_dev(act_q_ndt_value/act_q_nav_value, 100), densify(mdl10_group_name)), Decay: 0
2025-08-03 10:49:50,773 - SimulationService - WARNING - ❌ 任务 2 失败https://api.worldquantbrain.com/simulations/4mXP1Raax591bM6eKrsoRcw: {"children":["31jS1XcVW4Izc6s5tf1XdH8","1G1jRyf7A4yUcnk2nJel8ZO","35xHm1gv15cD9h8rKi39J34"],"type":"REGULAR","status":"ERROR"}
2025-08-03 10:49:50,774 - SimulationService - INFO - 正在查询任务 2 的 3 个子任务详情...
2025-08-03 10:49:51,087 - SimulationService - INFO - 📄 任务 2 子任务 1/3 (ID: 31jS1XcVW4Izc6s5tf1XdH8):
2025-08-03 10:49:51,087 - SimulationService - INFO -    状态: ERROR
2025-08-03 10:49:51,087 - SimulationService - ERROR -    错误信息: Attempted to use unknown variable "act_q_ndt_value"
2025-08-03 10:49:52,042 - SimulationService - INFO - 📄 任务 2 子任务 2/3 (ID: 1G1jRyf7A4yUcnk2nJel8ZO):
2025-08-03 10:49:52,042 - SimulationService - INFO -    状态: ERROR
2025-08-03 10:49:52,042 - SimulationService - ERROR -    错误信息: Attempted to use unknown variable "act_q_ndt_value"
2025-08-03 10:49:52,887 - SimulationService - INFO - 📄 任务 2 子任务 3/3 (ID: 35xHm1gv15cD9h8rKi39J34):
2025-08-03 10:49:52,887 - SimulationService - INFO -    状态: ERROR
2025-08-03 10:49:52,887 - SimulationService - ERROR -    错误信息: Attempted to use unknown variable "act_q_ndt_value"
2025-08-03 10:49:53,392 - SimulationService - INFO - 所有仿真任务完成，成功完成 0 个Alpha
2025-08-03 10:49:53,392 - SimulationService - WARNING - 第 1 批仿真无成功的Alpha
2025-08-03 10:49:53,392 - SimulationService - INFO - 批次 1 完成，总进度: 100%
2025-08-03 10:49:53,393 - __main__ - INFO - Alpha voQR35v 二阶优化仿真进度: 100%
2025-08-03 10:49:53,393 - __main__ - INFO - Alpha voQR35v 二阶优化成功，生成了 23 个Alpha
2025-08-03 10:49:53,393 - __main__ - INFO - 开始对Alpha voQR35v 进行三阶优化
2025-08-03 10:49:54,732 - root - INFO - 登录成功
2025-08-03 10:49:54,732 - root - INFO - 响应内容: {"user":{"id":"YK49234"},"token":{"expiry":14400.0},"permissions":["BEFORE_AND_AFTER_PERFORMANCE_V2","BRAIN_LABS","BRAIN_LABS_JUPYTER_LAB","CONSULTANT","MULTI_SIMULATION","PROD_ALPHAS","REFERRAL","VISUALIZATION","WORKDAY"]}
2025-08-03 10:49:55,278 - __main__ - INFO - 成功获取Alpha voQR35v 信息
2025-08-03 10:49:55,283 - root - INFO - 已过滤掉 0 个已回测的alpha，剩余 32 个新alpha待回测
2025-08-03 10:49:55,283 - __main__ - INFO - 为Alpha voQR35v 生成了 32 个三阶Alpha
2025-08-03 10:49:55,283 - SimulationService - INFO - 开始批量仿真，批次大小: 1000
2025-08-03 10:49:55,283 - SimulationService - INFO - 进行第 1/1 批仿真，数量: 32
2025-08-03 10:49:55,283 - SimulationService - INFO - 开始仿真 32 个Alpha
2025-08-03 10:49:55,283 - SimulationService - INFO - 准备仿真 4 个任务
2025-08-03 10:49:55,755 - SimulationService - INFO - 任务 0 提交成功
2025-08-03 10:49:56,267 - SimulationService - INFO - 任务 1 提交成功
2025-08-03 10:49:56,787 - SimulationService - INFO - 任务 2 提交成功
2025-08-03 10:49:57,606 - SimulationService - INFO - 任务 3 提交成功
2025-08-03 10:50:02,981 - SimulationService - ERROR - ❌ 任务 0 失败，包含的Alpha表达式：
2025-08-03 10:50:02,981 - SimulationService - ERROR -    - Alpha: trade_when(ts_arg_max(volume, 5) == 0, ts_std_dev(act_q_ndt_value/act_q_nav_value, 100), abs(returns) > 0.1), Decay: 0
2025-08-03 10:50:02,981 - SimulationService - ERROR -    - Alpha: trade_when(ts_arg_max(volume, 5) == 0, ts_std_dev(act_q_ndt_value/act_q_nav_value, 100), -1), Decay: 0
2025-08-03 10:50:02,981 - SimulationService - ERROR -    - Alpha: trade_when(ts_corr(close, volume, 20) < 0, ts_std_dev(act_q_ndt_value/act_q_nav_value, 100), abs(returns) > 0.1), Decay: 0
2025-08-03 10:50:02,981 - SimulationService - ERROR -    - Alpha: trade_when(ts_corr(close, volume, 20) < 0, ts_std_dev(act_q_ndt_value/act_q_nav_value, 100), -1), Decay: 0
2025-08-03 10:50:02,982 - SimulationService - ERROR -    - Alpha: trade_when(ts_corr(close, volume, 5) < 0, ts_std_dev(act_q_ndt_value/act_q_nav_value, 100), abs(returns) > 0.1), Decay: 0
2025-08-03 10:50:02,982 - SimulationService - ERROR -    - Alpha: trade_when(ts_corr(close, volume, 5) < 0, ts_std_dev(act_q_ndt_value/act_q_nav_value, 100), -1), Decay: 0
2025-08-03 10:50:02,982 - SimulationService - ERROR -    - Alpha: trade_when(ts_mean(volume,10)>ts_mean(volume,60), ts_std_dev(act_q_ndt_value/act_q_nav_value, 100), abs(returns) > 0.1), Decay: 0
2025-08-03 10:50:02,982 - SimulationService - ERROR -    - Alpha: trade_when(ts_mean(volume,10)>ts_mean(volume,60), ts_std_dev(act_q_ndt_value/act_q_nav_value, 100), -1), Decay: 0
2025-08-03 10:50:02,982 - SimulationService - ERROR -    - Alpha: trade_when(group_rank(ts_std_dev(returns,60), sector) > 0.7, ts_std_dev(act_q_ndt_value/act_q_nav_value, 100), abs(returns) > 0.1), Decay: 0
2025-08-03 10:50:02,982 - SimulationService - ERROR -    - Alpha: trade_when(group_rank(ts_std_dev(returns,60), sector) > 0.7, ts_std_dev(act_q_ndt_value/act_q_nav_value, 100), -1), Decay: 0
2025-08-03 10:50:02,982 - SimulationService - WARNING - ❌ 任务 0 失败https://api.worldquantbrain.com/simulations/2sXhpJ8mz52S9Gqt3hYBvzo: {"children":["3PqaS44nl57magIHSxyXAbU","2YdUmP5Rf4Bm9uh9EwKjY7k","2qW1IU8xT4tLcko8cR7oS9b","3PxYW93wJ4tvbU26Lpxhds9","3SQpYn5Lg4BP9AZ5Zo9ofgN","1E9ZJj5Gk58xck2eRrK9dS7","30Mytu7Oj4vaatEceJoKtmw","3Uww2r4k74Dr9UHvt99bEZs","4AvyVenM4QBayI8sEUCPWU","16cadYaTm5cb8Dg140KLpXKh"],"type":"REGULAR","status":"ERROR"}
2025-08-03 10:50:02,982 - SimulationService - INFO - 正在查询任务 0 的 10 个子任务详情...
2025-08-03 10:50:03,351 - SimulationService - INFO - 📄 任务 0 子任务 1/10 (ID: 3PqaS44nl57magIHSxyXAbU):
2025-08-03 10:50:03,352 - SimulationService - INFO -    状态: ERROR
2025-08-03 10:50:03,352 - SimulationService - ERROR -    错误信息: Attempted to use unknown variable "act_q_ndt_value"
2025-08-03 10:50:04,254 - SimulationService - INFO - 📄 任务 0 子任务 2/10 (ID: 2YdUmP5Rf4Bm9uh9EwKjY7k):
2025-08-03 10:50:04,254 - SimulationService - INFO -    状态: ERROR
2025-08-03 10:50:04,254 - SimulationService - ERROR -    错误信息: Attempted to use unknown variable "act_q_ndt_value"
2025-08-03 10:50:05,688 - SimulationService - INFO - 📄 任务 0 子任务 3/10 (ID: 2qW1IU8xT4tLcko8cR7oS9b):
2025-08-03 10:50:05,688 - SimulationService - INFO -    状态: ERROR
2025-08-03 10:50:05,688 - SimulationService - ERROR -    错误信息: Attempted to use unknown variable "act_q_ndt_value"
2025-08-03 10:50:06,504 - SimulationService - INFO - 📄 任务 0 子任务 4/10 (ID: 3PxYW93wJ4tvbU26Lpxhds9):
2025-08-03 10:50:06,504 - SimulationService - INFO -    状态: ERROR
2025-08-03 10:50:06,504 - SimulationService - ERROR -    错误信息: Attempted to use unknown variable "act_q_ndt_value"
2025-08-03 10:50:07,428 - SimulationService - INFO - 📄 任务 0 子任务 5/10 (ID: 3SQpYn5Lg4BP9AZ5Zo9ofgN):
2025-08-03 10:50:07,429 - SimulationService - INFO -    状态: ERROR
2025-08-03 10:50:07,429 - SimulationService - ERROR -    错误信息: Attempted to use unknown variable "act_q_ndt_value"
2025-08-03 10:50:08,406 - SimulationService - INFO - 📄 任务 0 子任务 6/10 (ID: 1E9ZJj5Gk58xck2eRrK9dS7):
2025-08-03 10:50:08,406 - SimulationService - INFO -    状态: ERROR
2025-08-03 10:50:08,406 - SimulationService - ERROR -    错误信息: Attempted to use unknown variable "act_q_ndt_value"
2025-08-03 10:50:09,241 - SimulationService - INFO - 📄 任务 0 子任务 7/10 (ID: 30Mytu7Oj4vaatEceJoKtmw):
2025-08-03 10:50:09,241 - SimulationService - INFO -    状态: ERROR
2025-08-03 10:50:09,241 - SimulationService - ERROR -    错误信息: Attempted to use unknown variable "act_q_ndt_value"
2025-08-03 10:50:10,083 - SimulationService - INFO - 📄 任务 0 子任务 8/10 (ID: 3Uww2r4k74Dr9UHvt99bEZs):
2025-08-03 10:50:10,083 - SimulationService - INFO -    状态: ERROR
2025-08-03 10:50:10,083 - SimulationService - ERROR -    错误信息: Attempted to use unknown variable "act_q_ndt_value"
2025-08-03 10:50:10,921 - SimulationService - INFO - 📄 任务 0 子任务 9/10 (ID: 4AvyVenM4QBayI8sEUCPWU):
2025-08-03 10:50:10,922 - SimulationService - INFO -    状态: ERROR
2025-08-03 10:50:10,923 - SimulationService - ERROR -    错误信息: Attempted to use unknown variable "act_q_ndt_value"
2025-08-03 10:50:11,851 - SimulationService - INFO - 📄 任务 0 子任务 10/10 (ID: 16cadYaTm5cb8Dg140KLpXKh):
2025-08-03 10:50:11,851 - SimulationService - INFO -    状态: ERROR
2025-08-03 10:50:11,851 - SimulationService - ERROR -    错误信息: Attempted to use unknown variable "act_q_ndt_value"
2025-08-03 10:50:12,671 - SimulationService - ERROR - ❌ 任务 1 失败，包含的Alpha表达式：
2025-08-03 10:50:12,671 - SimulationService - ERROR -    - Alpha: trade_when(ts_zscore(returns,60) > 2, ts_std_dev(act_q_ndt_value/act_q_nav_value, 100), abs(returns) > 0.1), Decay: 0
2025-08-03 10:50:12,671 - SimulationService - ERROR -    - Alpha: trade_when(ts_zscore(returns,60) > 2, ts_std_dev(act_q_ndt_value/act_q_nav_value, 100), -1), Decay: 0
2025-08-03 10:50:12,671 - SimulationService - ERROR -    - Alpha: trade_when(ts_arg_min(volume, 5) > 3, ts_std_dev(act_q_ndt_value/act_q_nav_value, 100), abs(returns) > 0.1), Decay: 0
2025-08-03 10:50:12,671 - SimulationService - ERROR -    - Alpha: trade_when(ts_arg_min(volume, 5) > 3, ts_std_dev(act_q_ndt_value/act_q_nav_value, 100), -1), Decay: 0
2025-08-03 10:50:12,672 - SimulationService - ERROR -    - Alpha: trade_when(ts_std_dev(returns, 5) > ts_std_dev(returns, 20), ts_std_dev(act_q_ndt_value/act_q_nav_value, 100), abs(returns) > 0.1), Decay: 0
2025-08-03 10:50:12,672 - SimulationService - ERROR -    - Alpha: trade_when(ts_std_dev(returns, 5) > ts_std_dev(returns, 20), ts_std_dev(act_q_ndt_value/act_q_nav_value, 100), -1), Decay: 0
2025-08-03 10:50:12,672 - SimulationService - ERROR -    - Alpha: trade_when(ts_arg_max(close, 5) == 0, ts_std_dev(act_q_ndt_value/act_q_nav_value, 100), abs(returns) > 0.1), Decay: 0
2025-08-03 10:50:12,672 - SimulationService - ERROR -    - Alpha: trade_when(ts_arg_max(close, 5) == 0, ts_std_dev(act_q_ndt_value/act_q_nav_value, 100), -1), Decay: 0
2025-08-03 10:50:12,672 - SimulationService - ERROR -    - Alpha: trade_when(ts_arg_max(close, 20) == 0, ts_std_dev(act_q_ndt_value/act_q_nav_value, 100), abs(returns) > 0.1), Decay: 0
2025-08-03 10:50:12,672 - SimulationService - ERROR -    - Alpha: trade_when(ts_arg_max(close, 20) == 0, ts_std_dev(act_q_ndt_value/act_q_nav_value, 100), -1), Decay: 0
2025-08-03 10:50:12,672 - SimulationService - WARNING - ❌ 任务 1 失败https://api.worldquantbrain.com/simulations/Wu0iI88N4CFb5HkKIm9pmz: {"children":["1wyRlUeTA4gxb8FAZcB4eoV","25xXF1b8c5aDaKQ193x1RyK5","KltWw5e94kTc1pRtucmQVU","4rpgIM4kx5gTayOpzfkqY1k","12Y6zPf3l4Pz9E7VjIOkt4m","vebrMalh4ESbxrWJ4J7f34","4spLzGdEn4OlaXo6JyhwweF","YD7VV9Cp4qecjT147Z8UBrM","1Qmobae4X5hi9wkSuTeSFvi","12fKNM7z85gQckZb1bDyGXo"],"type":"REGULAR","status":"ERROR"}
2025-08-03 10:50:12,672 - SimulationService - INFO - 正在查询任务 1 的 10 个子任务详情...
2025-08-03 10:50:13,163 - SimulationService - INFO - 📄 任务 1 子任务 1/10 (ID: 1wyRlUeTA4gxb8FAZcB4eoV):
2025-08-03 10:50:13,164 - SimulationService - INFO -    状态: ERROR
2025-08-03 10:50:13,164 - SimulationService - ERROR -    错误信息: Attempted to use unknown variable "act_q_ndt_value"
2025-08-03 10:50:14,083 - SimulationService - INFO - 📄 任务 1 子任务 2/10 (ID: 25xXF1b8c5aDaKQ193x1RyK5):
2025-08-03 10:50:14,083 - SimulationService - INFO -    状态: ERROR
2025-08-03 10:50:14,083 - SimulationService - ERROR -    错误信息: Attempted to use unknown variable "act_q_ndt_value"
2025-08-03 10:50:14,968 - SimulationService - INFO - 📄 任务 1 子任务 3/10 (ID: KltWw5e94kTc1pRtucmQVU):
2025-08-03 10:50:14,968 - SimulationService - INFO -    状态: ERROR
2025-08-03 10:50:14,968 - SimulationService - ERROR -    错误信息: Attempted to use unknown variable "act_q_ndt_value"
2025-08-03 10:50:15,826 - SimulationService - INFO - 📄 任务 1 子任务 4/10 (ID: 4rpgIM4kx5gTayOpzfkqY1k):
2025-08-03 10:50:15,826 - SimulationService - INFO -    状态: ERROR
2025-08-03 10:50:15,827 - SimulationService - ERROR -    错误信息: Attempted to use unknown variable "act_q_ndt_value"
2025-08-03 10:50:16,669 - SimulationService - INFO - 📄 任务 1 子任务 5/10 (ID: 12Y6zPf3l4Pz9E7VjIOkt4m):
2025-08-03 10:50:16,669 - SimulationService - INFO -    状态: ERROR
2025-08-03 10:50:16,669 - SimulationService - ERROR -    错误信息: Attempted to use unknown variable "act_q_ndt_value"
2025-08-03 10:50:17,828 - SimulationService - INFO - 📄 任务 1 子任务 6/10 (ID: vebrMalh4ESbxrWJ4J7f34):
2025-08-03 10:50:17,828 - SimulationService - INFO -    状态: ERROR
2025-08-03 10:50:17,829 - SimulationService - ERROR -    错误信息: Attempted to use unknown variable "act_q_ndt_value"
2025-08-03 10:50:21,192 - SimulationService - INFO - 📄 任务 1 子任务 7/10 (ID: 4spLzGdEn4OlaXo6JyhwweF):
2025-08-03 10:50:21,193 - SimulationService - INFO -    状态: ERROR
2025-08-03 10:50:21,193 - SimulationService - ERROR -    错误信息: Attempted to use unknown variable "act_q_ndt_value"
2025-08-03 10:50:23,266 - SimulationService - INFO - 📄 任务 1 子任务 8/10 (ID: YD7VV9Cp4qecjT147Z8UBrM):
2025-08-03 10:50:23,266 - SimulationService - INFO -    状态: ERROR
2025-08-03 10:50:23,266 - SimulationService - ERROR -    错误信息: Attempted to use unknown variable "act_q_ndt_value"
2025-08-03 10:50:25,009 - SimulationService - INFO - 📄 任务 1 子任务 9/10 (ID: 1Qmobae4X5hi9wkSuTeSFvi):
2025-08-03 10:50:25,009 - SimulationService - INFO -    状态: ERROR
2025-08-03 10:50:25,009 - SimulationService - ERROR -    错误信息: Attempted to use unknown variable "act_q_ndt_value"
2025-08-03 10:50:25,897 - SimulationService - INFO - 📄 任务 1 子任务 10/10 (ID: 12fKNM7z85gQckZb1bDyGXo):
2025-08-03 10:50:25,897 - SimulationService - INFO -    状态: ERROR
2025-08-03 10:50:25,897 - SimulationService - ERROR -    错误信息: Attempted to use unknown variable "act_q_ndt_value"
2025-08-03 10:50:27,421 - SimulationService - ERROR - ❌ 任务 2 失败，包含的Alpha表达式：
2025-08-03 10:50:27,421 - SimulationService - ERROR -    - Alpha: trade_when(ts_corr(close, volume, 5) > 0, ts_std_dev(act_q_ndt_value/act_q_nav_value, 100), abs(returns) > 0.1), Decay: 0
2025-08-03 10:50:27,421 - SimulationService - ERROR -    - Alpha: trade_when(ts_corr(close, volume, 5) > 0, ts_std_dev(act_q_ndt_value/act_q_nav_value, 100), -1), Decay: 0
2025-08-03 10:50:27,421 - SimulationService - ERROR -    - Alpha: trade_when(ts_corr(close, volume, 5) > 0.3, ts_std_dev(act_q_ndt_value/act_q_nav_value, 100), abs(returns) > 0.1), Decay: 0
2025-08-03 10:50:27,422 - SimulationService - ERROR -    - Alpha: trade_when(ts_corr(close, volume, 5) > 0.3, ts_std_dev(act_q_ndt_value/act_q_nav_value, 100), -1), Decay: 0
2025-08-03 10:50:27,422 - SimulationService - ERROR -    - Alpha: trade_when(ts_corr(close, volume, 5) > 0.5, ts_std_dev(act_q_ndt_value/act_q_nav_value, 100), abs(returns) > 0.1), Decay: 0
2025-08-03 10:50:27,422 - SimulationService - ERROR -    - Alpha: trade_when(ts_corr(close, volume, 5) > 0.5, ts_std_dev(act_q_ndt_value/act_q_nav_value, 100), -1), Decay: 0
2025-08-03 10:50:27,422 - SimulationService - ERROR -    - Alpha: trade_when(ts_corr(close, volume, 20) > 0, ts_std_dev(act_q_ndt_value/act_q_nav_value, 100), abs(returns) > 0.1), Decay: 0
2025-08-03 10:50:27,422 - SimulationService - ERROR -    - Alpha: trade_when(ts_corr(close, volume, 20) > 0, ts_std_dev(act_q_ndt_value/act_q_nav_value, 100), -1), Decay: 0
2025-08-03 10:50:27,422 - SimulationService - ERROR -    - Alpha: trade_when(ts_corr(close, volume, 20) > 0.3, ts_std_dev(act_q_ndt_value/act_q_nav_value, 100), abs(returns) > 0.1), Decay: 0
2025-08-03 10:50:27,422 - SimulationService - ERROR -    - Alpha: trade_when(ts_corr(close, volume, 20) > 0.3, ts_std_dev(act_q_ndt_value/act_q_nav_value, 100), -1), Decay: 0
2025-08-03 10:50:27,422 - SimulationService - WARNING - ❌ 任务 2 失败https://api.worldquantbrain.com/simulations/1gNoia2XZ5iD9yZG6xQJwQG: {"children":["1PpkCE46o55BbAH8XVJb3YH","3TDsVl6ye50K8RsthznC5Vn","1wbNn1Rj4EMbvUHa2yYVXY","3SIwRDaa555H9EhIWJlOLxj","3JKsSaz64FZ9F912eGEcvPE","2kOycs4wH4tHaEd1htH1yUw0","1rPuGQ3P45g3cjsOdxZHmtb","3rFcBy4c4nhbZ5FInSyQK1","4Eb2IdebO53maafO4C6YPpm","1EFNIMfEQ5bY9ndV5gzcxAU"],"type":"REGULAR","status":"ERROR"}
2025-08-03 10:50:27,422 - SimulationService - INFO - 正在查询任务 2 的 10 个子任务详情...
2025-08-03 10:50:29,641 - SimulationService - INFO - 📄 任务 2 子任务 1/10 (ID: 1PpkCE46o55BbAH8XVJb3YH):
2025-08-03 10:50:29,641 - SimulationService - INFO -    状态: ERROR
2025-08-03 10:50:29,641 - SimulationService - ERROR -    错误信息: Attempted to use unknown variable "act_q_ndt_value"
2025-08-03 10:50:30,978 - SimulationService - INFO - 📄 任务 2 子任务 2/10 (ID: 3TDsVl6ye50K8RsthznC5Vn):
2025-08-03 10:50:30,979 - SimulationService - INFO -    状态: ERROR
2025-08-03 10:50:30,979 - SimulationService - ERROR -    错误信息: Attempted to use unknown variable "act_q_ndt_value"
2025-08-03 10:50:34,860 - SimulationService - INFO - 📄 任务 2 子任务 3/10 (ID: 1wbNn1Rj4EMbvUHa2yYVXY):
2025-08-03 10:50:34,861 - SimulationService - INFO -    状态: ERROR
2025-08-03 10:50:34,861 - SimulationService - ERROR -    错误信息: Attempted to use unknown variable "act_q_ndt_value"
